import * as THREE from 'three';
import type { World, RigidBody } from '@dimforge/rapier3d-compat';

/**
 * 骰子面的纹理配置
 */
export interface DiceFaceTexture {
  /** 基础颜色纹理 */
  map?: THREE.Texture;
  /** 法线贴图 */
  normalMap?: THREE.Texture;
  /** 粗糙度贴图 */
  roughnessMap?: THREE.Texture;
  /** 金属度贴图 */
  metalnessMap?: THREE.Texture;
  /** 基础颜色 */
  color?: THREE.Color;
}

/**
 * 单个骰子的配置
 */
export interface DiceConfig {
  /** 骰子ID */
  id: string;
  /** 初始位置 */
  position: THREE.Vector3;
  /** 初始旋转 */
  rotation: THREE.Euler;
  /** 骰子大小 */
  size: number;
  /** 六个面的纹理配置 */
  faces: [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture];
  /** 物理属性 */
  physics: {
    /** 质量 */
    mass: number;
    /** 摩擦系数 */
    friction: number;
    /** 弹性系数 */
    restitution: number;
  };
}

/**
 * 容器边界配置
 */
export interface ContainerConfig {
  /** 容器大小 */
  size: THREE.Vector3;
  /** 容器位置 */
  position: THREE.Vector3;
  /** 是否可见（调试用） */
  visible: boolean;
  /** 物理属性 */
  physics: {
    /** 摩擦系数 */
    friction: number;
    /** 弹性系数 */
    restitution: number;
  };
}

/**
 * 光照配置
 */
export interface LightingConfig {
  /** 环境光强度 */
  ambientIntensity: number;
  /** 环境光颜色 */
  ambientColor: THREE.Color;
  /** 主光源强度 */
  directionalIntensity: number;
  /** 主光源颜色 */
  directionalColor: THREE.Color;
  /** 主光源位置 */
  directionalPosition: THREE.Vector3;
  /** 是否启用阴影 */
  shadows: boolean;
}

/**
 * 渲染器配置
 */
export interface RendererConfig {
  /** 抗锯齿 */
  antialias: boolean;
  /** 透明背景 */
  alpha: boolean;
  /** 像素比 */
  pixelRatio: number;
  /** 阴影类型 */
  shadowType: THREE.ShadowMapType;
  /** 色调映射 */
  toneMapping: THREE.ToneMapping;
  /** 曝光度 */
  toneMappingExposure: number;
}

/**
 * 物理引擎配置
 */
export interface PhysicsConfig {
  /** 重力 */
  gravity: THREE.Vector3;
  /** 时间步长 */
  timeStep: number;
  /** 最大子步数 */
  maxSubSteps: number;
}

/**
 * 骰子模拟器的完整配置
 */
export interface DiceSimulationConfig {
  /** 骰子配置数组 */
  dices: DiceConfig[];
  /** 容器配置 */
  container: ContainerConfig;
  /** 光照配置 */
  lighting: LightingConfig;
  /** 渲染器配置 */
  renderer: RendererConfig;
  /** 物理引擎配置 */
  physics: PhysicsConfig;
  /** 性能配置 */
  performance: {
    /** 目标帧率 */
    targetFPS: number;
    /** 静止时的帧率 */
    idleFPS: number;
    /** 是否启用自适应质量 */
    adaptiveQuality: boolean;
    /** 最小质量级别 */
    minQuality: number;
    /** 是否启用运动模糊 */
    motionBlur: boolean;
  };
}

/**
 * 骰子实例数据
 */
export interface DiceInstance {
  /** 配置 */
  config: DiceConfig;
  /** Three.js网格 */
  mesh: THREE.Mesh;
  /** Rapier刚体 */
  rigidBody: RigidBody;
  /** 材质数组 */
  materials: THREE.Material[];
}

/**
 * 力的施加配置
 */
export interface ForceConfig {
  /** 力的方向和大小 */
  force: THREE.Vector3;
  /** 施加点（相对于骰子中心） */
  point?: THREE.Vector3;
  /** 是否为冲量 */
  impulse?: boolean;
}

/**
 * 键盘输入事件数据
 */
export interface KeyboardInputEvent {
  /** 输入的字符 */
  key: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 骰子模拟器事件类型
 */
export interface DiceSimulationEvents {
  /** 骰子碰撞事件 */
  collision: {
    diceId: string;
    otherDiceId?: string;
    force: number;
  };
  /** 骰子静止事件 */
  rest: {
    diceId: string;
    finalRotation: THREE.Euler;
  };
  /** 性能警告事件 */
  performanceWarning: {
    fps: number;
    reason: string;
  };
}

/**
 * 默认配置常量
 */
export const DEFAULT_DICE_CONFIG = {
  rotation: new THREE.Euler(0, 0, 0),
  size: 1,
  physics: {
    mass: 2.0, // 增加质量，更有重量感
    friction: 0.5, // 增加摩擦，减少滑动
    restitution: 0.2, // 减少弹性，更真实的反弹
  },
} as const;

export const DEFAULT_CONTAINER_CONFIG: ContainerConfig = {
  size: new THREE.Vector3(8, 8, 8),
  position: new THREE.Vector3(0, 0, 0),
  visible: false, // 关闭绿色线框显示
  physics: {
    friction: 0.5,
    restitution: 0.2,
  },
};

export const DEFAULT_LIGHTING_CONFIG: LightingConfig = {
  ambientIntensity: 0.6, // 增加环境光
  ambientColor: new THREE.Color(0xffffff),
  directionalIntensity: 2.6, // 增加方向光强度
  directionalColor: new THREE.Color(0xffffff),
  directionalPosition: new THREE.Vector3(5, 10, 5),
  shadows: true,
};

export const DEFAULT_RENDERER_CONFIG: RendererConfig = {
  antialias: true,
  alpha: true,
  pixelRatio: Math.min((typeof window !== 'undefined' ? window.devicePixelRatio : 1) || 1, 2),
  shadowType: THREE.PCFShadowMap, // 使用更高性能的阴影类型
  toneMapping: THREE.NoToneMapping,
  toneMappingExposure: 1.2,
};

export const DEFAULT_PHYSICS_CONFIG: PhysicsConfig = {
  gravity: new THREE.Vector3(0, -15, 0), // 增强重力，让骰子更快下落
  timeStep: 1/60,
  maxSubSteps: 3,
};
