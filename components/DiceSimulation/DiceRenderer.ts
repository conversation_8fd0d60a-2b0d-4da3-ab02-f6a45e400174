import * as THREE from 'three';
import type {
  RendererConfig,
  LightingConfig,
  ContainerConfig,
  DiceConfig,
  DiceFaceTexture
} from './types';

/**
 * 骰子Three.js渲染器类
 * 负责管理Three.js场景、相机、光照和渲染
 */
export class DiceRenderer {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private container: HTMLElement;
  private config: RendererConfig;
  private lightingConfig: LightingConfig;
  
  // 光照对象
  private ambientLight: THREE.AmbientLight;
  private directionalLight: THREE.DirectionalLight;
  
  // 容器可视化（调试用）
  private containerMesh: THREE.Mesh | null = null;

  // 地面平面（用于接收阴影）
  private groundPlane: THREE.Mesh | null = null;

  // 性能监控
  private frameCount = 0;
  private lastFPSCheck = 0;
  private currentFPS = 30;

  // 几何体缓存
  private static geometryCache = new Map<string, THREE.BufferGeometry>();

  // 像素化后处理已移除

  constructor(
    container: HTMLElement,
    rendererConfig: RendererConfig,
    lightingConfig: LightingConfig
  ) {
    this.container = container;
    this.config = rendererConfig;
    this.lightingConfig = lightingConfig;

    // 初始化Three.js组件
    this.scene = new THREE.Scene();
    this.camera = this.createCamera();
    this.renderer = this.createRenderer();
    this.ambientLight = this.createAmbientLight();
    this.directionalLight = this.createDirectionalLight();

    this.setupScene();
    this.setupEventListeners();
  }

  // 像素化效果设置已移除

  /**
   * 创建相机
   */
  private createCamera(): THREE.PerspectiveCamera {
    const camera = new THREE.PerspectiveCamera(
      72, // FOV
      this.container.clientWidth / this.container.clientHeight, // 宽高比
      0.1, // 近裁剪面
      50 // 远裁剪面
    );

    // 设置相机位置，俯视角度观察骰子
    camera.position.set(0, 1.5, 5); // 降低相机高度，拉近距离
    camera.lookAt(0, -4, 0);

    return camera;
  }

  /**
   * 创建渲染器
   */
  private createRenderer(): THREE.WebGLRenderer {
    const renderer = new THREE.WebGLRenderer({
      antialias: this.config.antialias,
      alpha: this.config.alpha,
      powerPreference: 'high-performance'
    });

    renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    renderer.setPixelRatio(this.config.pixelRatio);
    
    // 设置透明背景
    renderer.setClearColor(0x000000, 0);
    
    // 启用阴影
    if (this.lightingConfig.shadows) {
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = this.config.shadowType;
    }

    // 设置正确的颜色空间
    renderer.outputColorSpace = THREE.SRGBColorSpace;

    // 色调映射
    renderer.toneMapping = this.config.toneMapping;
    renderer.toneMappingExposure = this.config.toneMappingExposure;

    // 在较新版本的Three.js中，物理正确的光照是默认启用的
    // renderer.physicallyCorrectLights 已被移除

    return renderer;
  }

  /**
   * 创建环境光
   */
  private createAmbientLight(): THREE.AmbientLight {
    const light = new THREE.AmbientLight(
      this.lightingConfig.ambientColor,
      this.lightingConfig.ambientIntensity
    );
    return light;
  }

  /**
   * 创建方向光
   */
  private createDirectionalLight(): THREE.DirectionalLight {
    const light = new THREE.DirectionalLight(
      this.lightingConfig.directionalColor,
      this.lightingConfig.directionalIntensity
    );

    light.position.copy(this.lightingConfig.directionalPosition);
    light.target.position.set(0, 0, 0);

    // 配置阴影 - 优化性能
    if (this.lightingConfig.shadows) {
      light.castShadow = true;
      light.shadow.mapSize.width = 1024; // 降低阴影分辨率提高性能
      light.shadow.mapSize.height = 1024;
      light.shadow.camera.near = 0.5;
      light.shadow.camera.far = 30; // 减少阴影距离
      light.shadow.camera.left = -8;
      light.shadow.camera.right = 8;
      light.shadow.camera.top = 8;
      light.shadow.camera.bottom = -8;
      light.shadow.bias = -0.0001;
      light.shadow.radius = 4; // 添加软阴影
    }

    return light;
  }

  /**
   * 设置场景
   */
  private setupScene(): void {
    // 添加光照
    this.scene.add(this.ambientLight);
    this.scene.add(this.directionalLight);
    this.scene.add(this.directionalLight.target);

    // 将渲染器添加到容器
    this.container.appendChild(this.renderer.domElement);

    // 设置渲染器样式
    this.renderer.domElement.style.position = 'absolute';
    this.renderer.domElement.style.top = '0';
    this.renderer.domElement.style.left = '0';
    this.renderer.domElement.style.width = '100%';
    this.renderer.domElement.style.height = '100%';
    this.renderer.domElement.style.zIndex = '-10'; // 确保在terminal下方
    this.renderer.domElement.style.pointerEvents = 'none'; // 不拦截鼠标事件
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 窗口大小变化处理
    const handleResize = () => {
      const width = this.container.clientWidth;
      const height = this.container.clientHeight;

      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);

      // 像素化效果大小更新已移除
    };

    window.addEventListener('resize', handleResize);

    // 保存清理函数
    (this.renderer.domElement as any)._cleanup = () => {
      window.removeEventListener('resize', handleResize);
    };
  }

  /**
   * 创建骰子网格
   */
  createDiceMesh(config: DiceConfig): THREE.Mesh {
    // 使用几何体缓存提高性能
    const geometryKey = `box_${config.size}`;
    let geometry = DiceRenderer.geometryCache.get(geometryKey);

    if (!geometry) {
      geometry = new THREE.BoxGeometry(config.size, config.size, config.size);
      DiceRenderer.geometryCache.set(geometryKey, geometry);
    }
    
    // 创建材质数组 - 优化性能
    const materials = config.faces.map((faceTexture, index) => {
      const material = new THREE.MeshStandardMaterial({
        color: faceTexture.color || new THREE.Color(0xffffff),
        map: faceTexture.map || null,
        normalMap: faceTexture.normalMap || null,
        roughnessMap: faceTexture.roughnessMap || null,
        metalnessMap: faceTexture.metalnessMap || null,
        roughness: 0.7,
        metalness: 0.1,
        // 透明度和混合设置
        transparent: faceTexture.map ? true : false, // 有纹理时启用透明度
        alphaTest: faceTexture.map ? 0.1 : 0, // 设置alpha测试阈值
        side: THREE.FrontSide, // 只渲染正面
      });

      // 如果有纹理贴图，调整混合模式以避免颜色混合
      if (faceTexture.map) {
        // 设置纹理的混合操作为叠加模式而非乘法模式
        material.map = faceTexture.map;

        // 使用自定义的onBeforeCompile来修改shader，实现正确的叠加效果
        material.onBeforeCompile = (shader) => {
          // 修改fragment shader来实现正确的纹理叠加
          shader.fragmentShader = shader.fragmentShader.replace(
            '#include <map_fragment>',
            `
            #ifdef USE_MAP
              vec4 sampledDiffuseColor = texture2D( map, vMapUv );
              // 如果纹理的alpha大于阈值，使用纹理颜色，否则使用材质颜色
              if (sampledDiffuseColor.a > 0.1) {
                diffuseColor.rgb = sampledDiffuseColor.rgb;
                diffuseColor.a *= sampledDiffuseColor.a;
              }
              // 如果纹理是透明的，保持原始的材质颜色
            #endif
            `
          );
        };
      }

      return material;
    });

    const mesh = new THREE.Mesh(geometry, materials);
    
    // 设置初始位置和旋转
    mesh.position.copy(config.position);
    mesh.rotation.copy(config.rotation);
    
    // 启用阴影
    if (this.lightingConfig.shadows) {
      mesh.castShadow = true;
      mesh.receiveShadow = true;
    }

    // 运动模糊数据初始化已移除

    return mesh;
  }

  /**
   * 创建容器可视化网格（调试用）
   */
  createContainerMesh(config: ContainerConfig): THREE.Mesh | null {
    if (!config.visible) return null;

    const geometry = new THREE.BoxGeometry(config.size.x, config.size.y, config.size.z);
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.2
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(config.position);

    return mesh;
  }

  /**
   * 创建地面平面（用于接收阴影）
   */
  createGroundPlane(containerConfig: ContainerConfig): THREE.Mesh {
    // 创建一个大的平面几何体
    const planeSize = Math.max(containerConfig.size.x, containerConfig.size.z) * 2;
    const geometry = new THREE.PlaneGeometry(planeSize, planeSize);

    // 创建特殊的阴影接收材质
    const material = new THREE.ShadowMaterial({
      opacity: 0.3, // 阴影透明度
      transparent: true,
    });

    const plane = new THREE.Mesh(geometry, material);

    // 设置平面位置和旋转
    plane.rotation.x = -Math.PI / 2; // 旋转90度使其水平
    plane.position.y = containerConfig.position.y - containerConfig.size.y / 2; // 放在容器底部

    // 启用阴影接收
    if (this.lightingConfig.shadows) {
      plane.receiveShadow = true;
    }

    this.groundPlane = plane;
    return plane;
  }

  /**
   * 添加网格到场景
   */
  addToScene(object: THREE.Object3D): void {
    this.scene.add(object);
  }

  /**
   * 从场景移除网格
   */
  removeFromScene(object: THREE.Object3D): void {
    this.scene.remove(object);
  }

  // 材质透明度确保方法已移除

  /**
   * 渲染一帧
   */
  render(): void {
    // 直接渲染到屏幕
    this.renderer.render(this.scene, this.camera);
    this.updateFPS();
  }

  /**
   * 更新FPS计算
   */
  private updateFPS(): void {
    this.frameCount++;
    const now = performance.now();
    
    if (now - this.lastFPSCheck >= 1000) {
      this.currentFPS = this.frameCount;
      this.frameCount = 0;
      this.lastFPSCheck = now;
    }
  }

  /**
   * 获取当前FPS
   */
  getFPS(): number {
    return this.currentFPS;
  }

  /**
   * 更新光照配置
   */
  updateLighting(config: Partial<LightingConfig>): void {
    this.lightingConfig = { ...this.lightingConfig, ...config };

    // 更新环境光
    if (config.ambientIntensity !== undefined) {
      this.ambientLight.intensity = config.ambientIntensity;
    }
    if (config.ambientColor) {
      this.ambientLight.color = config.ambientColor;
    }

    // 更新方向光
    if (config.directionalIntensity !== undefined) {
      this.directionalLight.intensity = config.directionalIntensity;
    }
    if (config.directionalColor) {
      this.directionalLight.color = config.directionalColor;
    }
    if (config.directionalPosition) {
      this.directionalLight.position.copy(config.directionalPosition);
    }
  }

  /**
   * 更新相机位置
   */
  updateCamera(position: THREE.Vector3, target: THREE.Vector3): void {
    this.camera.position.copy(position);
    this.camera.lookAt(target);
  }

  // 像素化效果控制方法已移除

  /**
   * 获取场景对象
   */
  getScene(): THREE.Scene {
    return this.scene;
  }

  /**
   * 获取相机对象
   */
  getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  /**
   * 获取渲染器对象
   */
  getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }

  /**
   * 销毁渲染器
   */
  dispose(): void {
    // 清理地面平面
    if (this.groundPlane) {
      this.scene.remove(this.groundPlane);
      this.groundPlane = null;
    }

    // 清理事件监听器
    if ((this.renderer.domElement as any)._cleanup) {
      (this.renderer.domElement as any)._cleanup();
    }

    // 移除DOM元素
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }

    // 销毁Three.js对象
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose();
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });

    // 清理几何体缓存
    DiceRenderer.geometryCache.forEach(geometry => geometry.dispose());
    DiceRenderer.geometryCache.clear();

    this.renderer.dispose();
  }
}
