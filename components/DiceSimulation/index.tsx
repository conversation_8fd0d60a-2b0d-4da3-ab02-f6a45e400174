'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './DiceController';
import {
  createDefaultDiceSimulationConfig,
  checkWebGLSupport,
  getDevicePerformanceLevel,
  adjustConfigForPerformance,
  createTexturedDiceFaces,
  createExampleTextureConfigs,
  createCustomTextureDiceConfigs
} from './utils';
import type { DiceSimulationConfig, DiceSimulationEvents } from './types';

interface DiceSimulationProps {
  /** 容器类名 */
  className?: string;
  /** 自定义配置 */
  config?: Partial<DiceSimulationConfig>;
  /** 是否启用纹理 */
  enableTextures?: boolean;
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 键盘输入回调注册函数 */
  onKeyboardCallbackReady?: (callback: (key: string) => void) => (() => void) | void;
  /** 事件回调 */
  onDiceRest?: (data: DiceSimulationEvents['rest']) => void;
  onCollision?: (data: DiceSimulationEvents['collision']) => void;
  onPerformanceWarning?: (data: DiceSimulationEvents['performanceWarning']) => void;
}

/**
 * 骰子模拟组件
 * 提供完整的3D骰子物理模拟功能
 */
export default function DiceSimulation({
  className = '',
  config: customConfig,
  enableTextures = true,
  autoStart = true,
  onKeyboardCallbackReady,
  onDiceRest,
  onCollision,
  onPerformanceWarning,
}: DiceSimulationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const controllerRef = useRef<DiceController | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fps, setFPS] = useState(60);
  const keyboardUnsubscribeRef = useRef<(() => void) | null>(null);
  const initializationRef = useRef<boolean>(false); // 防止重复初始化

  /**
   * 处理键盘输入的回调函数
   */
  const handleKeyboardInput = useCallback((key: string) => {
    if (controllerRef.current) {
      controllerRef.current.handleKeyboardInput(key);
    }
  }, []);

  /**
   * 初始化骰子模拟器
   */
  const initializeSimulation = useCallback(async () => {
    if (!containerRef.current || initializationRef.current) return;

    initializationRef.current = true;

    try {
      setIsLoading(true);
      setError(null);

      // 检查WebGL支持
      if (!checkWebGLSupport()) {
        throw new Error('您的浏览器不支持WebGL，无法运行3D骰子模拟');
      }

      // 获取设备性能等级
      const performanceLevel = getDevicePerformanceLevel();

      // 创建配置
      let config = createDefaultDiceSimulationConfig();
      
      // 应用性能调整
      config = adjustConfigForPerformance(config, performanceLevel);

      // 应用自定义配置
      if (customConfig) {
        config = {
          ...config,
          ...customConfig,
          dices: customConfig.dices || config.dices,
          container: { ...config.container, ...customConfig.container },
          lighting: { ...config.lighting, ...customConfig.lighting },
          renderer: { ...config.renderer, ...customConfig.renderer },
          physics: { ...config.physics, ...customConfig.physics },
          performance: { ...config.performance, ...customConfig.performance },
        };
      }

      // 替换默认骰子创建 - 使用自定义纹理
      if (enableTextures) {
        try {
          // 创建自定义纹理配置
          const customTextureConfigs = createExampleTextureConfigs();

          // 创建带有自定义纹理的骰子配置
          const customDiceConfigs = await createCustomTextureDiceConfigs(customTextureConfigs);

          // 应用自定义骰子配置
          config.dices = customDiceConfigs;
        } catch (error) {
          // 静默降级到默认纹理

          // 降级到默认纹理
          config.dices = config.dices.map((diceConfig, index) => ({
            ...diceConfig,
            faces: createTexturedDiceFaces(index),
          }));
        }
      }

      // 创建控制器
      const controller = new DiceController(containerRef.current, config);

      // 注册事件监听器
      if (onDiceRest) {
        controller.on('rest', onDiceRest);
      }
      if (onCollision) {
        controller.on('collision', onCollision);
      }
      if (onPerformanceWarning) {
        controller.on('performanceWarning', onPerformanceWarning);
      }

      // 初始化控制器
      await controller.initialize();

      controllerRef.current = controller;

      // 自动开始动画
      if (autoStart) {
        controller.start();
      }

      // 设置初始化完成状态
      setIsInitialized(true);

      // 在控制器完全准备好后注册键盘回调
      if (onKeyboardCallbackReady) {
        const unsubscribe = onKeyboardCallbackReady(handleKeyboardInput);
        if (typeof unsubscribe === 'function') {
          keyboardUnsubscribeRef.current = unsubscribe;
        }
      }

      // 开始FPS监控
      const fpsInterval = setInterval(() => {
        if (controllerRef.current) {
          setFPS(controllerRef.current.getFPS());
        }
      }, 1000);

      // 保存清理函数
      (controller as any)._cleanup = () => {
        clearInterval(fpsInterval);
        if (keyboardUnsubscribeRef.current) {
          keyboardUnsubscribeRef.current();
          keyboardUnsubscribeRef.current = null;
        }
      };

    } catch (err) {
      console.error('DiceSimulation initialization failed');
      setError('Failed to load 3D dice simulation');
      initializationRef.current = false;
    } finally {
      setIsLoading(false);
    }
  }, [customConfig, enableTextures, autoStart, onKeyboardCallbackReady, onDiceRest, onCollision, onPerformanceWarning, handleKeyboardInput]);

  /**
   * 组件挂载时初始化
   */
  useEffect(() => {
    // 延迟初始化，确保DOM已渲染
    const timer = setTimeout(initializeSimulation, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, [initializeSimulation]);

  /**
   * 组件卸载时清理
   */
  useEffect(() => {
    return () => {
      if (controllerRef.current) {
        if ((controllerRef.current as any)._cleanup) {
          (controllerRef.current as any)._cleanup();
        }
        controllerRef.current.dispose();
        controllerRef.current = null;
      }

      if (keyboardUnsubscribeRef.current) {
        keyboardUnsubscribeRef.current();
        keyboardUnsubscribeRef.current = null;
      }

      initializationRef.current = false;
      setIsInitialized(false);
      setIsLoading(true);
      setError(null);
    };
  }, []);

  /**
   * 手动控制方法
   */
  const shakeAllDice = useCallback((strength: number = 10) => {
    if (controllerRef.current) {
      controllerRef.current.shakeAllDice(strength);
    }
  }, []);

  const resetAllDice = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.resetAllDice();
    }
  }, []);

  const start = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.start();
    }
  }, []);

  const stop = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.stop();
    }
  }, []);

  // 注意：如果需要暴露方法给父组件，应该使用forwardRef包装整个组件
  // 这里暂时移除useImperativeHandle，因为当前组件没有使用forwardRef

  if (error) {
    return (
      <div className={`dice-simulation-error ${className}`}>
        <style jsx>{`
          .dice-simulation-error {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: -1;
          }
        `}</style>
      </div>
    );
  }

  return (
    <>
      <div
        ref={containerRef}
        className={`dice-simulation-container ${className}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -1,
          pointerEvents: 'none',
        }}
      />
      
      {isLoading && (
        <div className="dice-simulation-loading">
          <div className="load2">
            <div className="line"></div>
            <div className="line"></div>
            <div className="line"></div>
          </div>
          <style jsx>{`
            .dice-simulation-loading {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: -1;
            }
            .load2 {
              width: 20px;
              height: 20px;
              position: relative;
            }
            .load2 .line {
              display: inline-block;
              width: 3px;
              height: 20px;
              border-radius: 2px;
              background: #666;
              animation: load2 1.4s infinite ease-in-out;
            }
            .load2 .line:nth-child(1) {
              animation-delay: -0.32s;
            }
            .load2 .line:nth-child(2) {
              animation-delay: -0.16s;
              margin: 0 2px;
            }
            .load2 .line:nth-child(3) {
              animation-delay: 0s;
            }
            @keyframes load2 {
              0%, 80%, 100% {
                height: 20px;
                transform: scaleY(0.4);
              }
              40% {
                height: 20px;
                transform: scaleY(1);
              }
            }
          `}</style>
        </div>
      )}

      {/* 开发模式下显示FPS */}
      {process.env.NODE_ENV === 'development' && isInitialized && (
        <div className="fps-counter">
          FPS: {fps}
          <style jsx>{`
            .fps-counter {
              position: absolute;
              top: 10px;
              right: 10px;
              background: rgba(0, 0, 0, 0.7);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-family: monospace;
              font-size: 12px;
              z-index: 1000;
              pointer-events: none;
            }
          `}</style>
        </div>
      )}
    </>
  );
}
