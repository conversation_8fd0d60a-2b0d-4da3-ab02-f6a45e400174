import * as THREE from 'three';
import { textureManager } from './TextureManager';
import type { DiceTextureConfig } from './TextureManager';
import type {
  DiceSimulationConfig,
  DiceConfig,
  DiceFaceTexture
} from './types';
import {
  DEFAULT_DICE_CONFIG,
  DEFAULT_CONTAINER_CONFIG,
  DEFAULT_LIGHTING_CONFIG,
  DEFAULT_RENDERER_CONFIG,
  DEFAULT_PHYSICS_CONFIG
} from './types';

/**
 * 创建默认的骰子模拟配置
 */
export function createDefaultDiceSimulationConfig(): DiceSimulationConfig {
  return {
    dices: createDefaultDiceConfigs(),
    container: { ...DEFAULT_CONTAINER_CONFIG },
    lighting: { ...DEFAULT_LIGHTING_CONFIG },
    renderer: { ...DEFAULT_RENDERER_CONFIG },
    physics: { ...DEFAULT_PHYSICS_CONFIG },
    performance: {
      targetFPS: 60, // 默认60帧，将动态检测系统帧率
      idleFPS: 5,    // 静止时降到5帧，极致节能
      adaptiveQuality: true,
      minQuality: 0.5,
      motionBlur: true, // 启用运动模糊
    },
  };
}

/**
 * 创建默认的三个骰子配置
 */
export function createDefaultDiceConfigs(): DiceConfig[] {
  const diceConfigs: DiceConfig[] = [];

  for (let i = 0; i < 3; i++) {
    // 为每个骰子设置不同的固定位置，避免重叠
    const positions = [
      new THREE.Vector3(-1.5, 3, 0),   // 骰子1：左侧
      new THREE.Vector3(0, 3, 1.5),    // 骰子2：中间偏后
      new THREE.Vector3(1.5, 3, -1.5), // 骰子3：右侧偏前
    ];

    const diceConfig: DiceConfig = {
      id: `dice-${i + 1}`,
      position: positions[i],
      rotation: new THREE.Euler(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      ),
      size: 1.2, // 适中的骰子尺寸
      faces: createDefaultDiceFaces(i),
      physics: {
        mass: 1,
        friction: 0.4,
        restitution: 0.3,
      },
    };

    diceConfigs.push(diceConfig);
  }

  return diceConfigs;
}

/**
 * 创建自定义纹理的骰子配置
 */
export async function createCustomTextureDiceConfigs(
  textureConfigs: DiceTextureConfig[]
): Promise<DiceConfig[]> {
  const diceConfigs: DiceConfig[] = [];

  for (let i = 0; i < textureConfigs.length; i++) {
    const textureConfig = textureConfigs[i];

    // 为每个骰子设置不同的固定位置
    const positions = [
      new THREE.Vector3(-1.5, 3, 0),   // 骰子1：左侧
      new THREE.Vector3(0, 3, 1.5),    // 骰子2：中间偏后
      new THREE.Vector3(1.5, 3, -1.5), // 骰子3：右侧偏前
    ];

    // 加载自定义纹理
    const faces = await textureManager.createDiceTextures(textureConfig);

    const diceConfig: DiceConfig = {
      id: textureConfig.diceId,
      position: positions[i] || new THREE.Vector3(i * 2 - 2, 3, 0),
      rotation: new THREE.Euler(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      ),
      size: 1.2,
      faces,
      physics: {
        mass: 1,
        friction: 0.4,
        restitution: 0.3,
      },
    };

    diceConfigs.push(diceConfig);
  }

  return diceConfigs;
}

/**
 * 创建默认的骰子面纹理
 */
export function createDefaultDiceFaces(diceIndex: number): [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture] {
  // 为每个骰子使用不同的颜色主题
  const colorThemes = [
    [0xff4444, 0x44ff44, 0x4444ff, 0xffff44, 0xff44ff, 0x44ffff], // 骰子1: 鲜艳色彩
    [0x8b4513, 0xdaa520, 0x2e8b57, 0x4682b4, 0x9932cc, 0xdc143c], // 骰子2: 自然色彩
    [0x2c3e50, 0x34495e, 0x7f8c8d, 0x95a5a6, 0xbdc3c7, 0xecf0f1], // 骰子3: 灰度色彩
  ];
  
  const colors = colorThemes[diceIndex % colorThemes.length];
  
  return colors.map((color, faceIndex) => ({
    color: new THREE.Color(color),
    // 可以在这里添加纹理贴图
    // map: createDotTexture(faceIndex + 1), // 创建点数纹理
    // normalMap: createNormalMap(faceIndex + 1), // 创建法线贴图
  })) as [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture];
}

/**
 * 创建骰子点数纹理
 */
export function createDotTexture(dotCount: number, size: number = 256): THREE.Texture {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d')!;
  
  // 背景
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, size, size);
  
  // 点的配置
  const dotRadius = size * 0.08;
  const margin = size * 0.2;
  const center = size * 0.5;
  
  ctx.fillStyle = '#000000';
  
  // 根据点数绘制不同的图案
  switch (dotCount) {
    case 1:
      // 中心一个点
      drawDot(ctx, center, center, dotRadius);
      break;
    case 2:
      // 对角线两个点
      drawDot(ctx, margin, margin, dotRadius);
      drawDot(ctx, size - margin, size - margin, dotRadius);
      break;
    case 3:
      // 对角线三个点
      drawDot(ctx, margin, margin, dotRadius);
      drawDot(ctx, center, center, dotRadius);
      drawDot(ctx, size - margin, size - margin, dotRadius);
      break;
    case 4:
      // 四个角
      drawDot(ctx, margin, margin, dotRadius);
      drawDot(ctx, size - margin, margin, dotRadius);
      drawDot(ctx, margin, size - margin, dotRadius);
      drawDot(ctx, size - margin, size - margin, dotRadius);
      break;
    case 5:
      // 四个角加中心
      drawDot(ctx, margin, margin, dotRadius);
      drawDot(ctx, size - margin, margin, dotRadius);
      drawDot(ctx, center, center, dotRadius);
      drawDot(ctx, margin, size - margin, dotRadius);
      drawDot(ctx, size - margin, size - margin, dotRadius);
      break;
    case 6:
      // 两列三行
      const leftX = margin;
      const rightX = size - margin;
      const topY = margin;
      const middleY = center;
      const bottomY = size - margin;
      
      drawDot(ctx, leftX, topY, dotRadius);
      drawDot(ctx, leftX, middleY, dotRadius);
      drawDot(ctx, leftX, bottomY, dotRadius);
      drawDot(ctx, rightX, topY, dotRadius);
      drawDot(ctx, rightX, middleY, dotRadius);
      drawDot(ctx, rightX, bottomY, dotRadius);
      break;
  }
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.generateMipmaps = false;
  texture.minFilter = THREE.LinearFilter;
  texture.magFilter = THREE.LinearFilter;
  
  return texture;
}

/**
 * 绘制圆点
 */
function drawDot(ctx: CanvasRenderingContext2D, x: number, y: number, radius: number): void {
  ctx.beginPath();
  ctx.arc(x, y, radius, 0, Math.PI * 2);
  ctx.fill();
}

/**
 * 创建法线贴图（模拟凹陷效果）
 */
export function createNormalMap(dotCount: number, size: number = 256): THREE.Texture {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d')!;
  
  // 创建法线贴图的基础（平面法线）
  const imageData = ctx.createImageData(size, size);
  const data = imageData.data;
  
  // 初始化为平面法线 (0.5, 0.5, 1.0) -> (128, 128, 255)
  for (let i = 0; i < data.length; i += 4) {
    data[i] = 128;     // R (X)
    data[i + 1] = 128; // G (Y)
    data[i + 2] = 255; // B (Z)
    data[i + 3] = 255; // A
  }
  
  // 在点的位置创建凹陷效果
  const dotRadius = size * 0.08;
  const margin = size * 0.2;
  const center = size * 0.5;
  
  const createDepression = (x: number, y: number) => {
    const radius = dotRadius * 1.5;
    for (let dy = -radius; dy <= radius; dy++) {
      for (let dx = -radius; dx <= radius; dx++) {
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance <= radius) {
          const px = Math.floor(x + dx);
          const py = Math.floor(y + dy);
          
          if (px >= 0 && px < size && py >= 0 && py < size) {
            const index = (py * size + px) * 4;
            const depth = 1 - (distance / radius);
            
            // 创建向内的法线
            const normalX = (dx / radius) * depth;
            const normalY = (dy / radius) * depth;
            const normalZ = Math.sqrt(1 - normalX * normalX - normalY * normalY);
            
            data[index] = Math.floor((normalX + 1) * 127.5);     // R
            data[index + 1] = Math.floor((normalY + 1) * 127.5); // G
            data[index + 2] = Math.floor(normalZ * 255);         // B
          }
        }
      }
    }
  };
  
  // 根据点数创建凹陷
  switch (dotCount) {
    case 1:
      createDepression(center, center);
      break;
    case 2:
      createDepression(margin, margin);
      createDepression(size - margin, size - margin);
      break;
    case 3:
      createDepression(margin, margin);
      createDepression(center, center);
      createDepression(size - margin, size - margin);
      break;
    case 4:
      createDepression(margin, margin);
      createDepression(size - margin, margin);
      createDepression(margin, size - margin);
      createDepression(size - margin, size - margin);
      break;
    case 5:
      createDepression(margin, margin);
      createDepression(size - margin, margin);
      createDepression(center, center);
      createDepression(margin, size - margin);
      createDepression(size - margin, size - margin);
      break;
    case 6:
      const leftX = margin;
      const rightX = size - margin;
      const topY = margin;
      const middleY = center;
      const bottomY = size - margin;
      
      createDepression(leftX, topY);
      createDepression(leftX, middleY);
      createDepression(leftX, bottomY);
      createDepression(rightX, topY);
      createDepression(rightX, middleY);
      createDepression(rightX, bottomY);
      break;
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.generateMipmaps = false;
  texture.minFilter = THREE.LinearFilter;
  texture.magFilter = THREE.LinearFilter;
  
  return texture;
}

/**
 * 创建带纹理的骰子面配置
 */
export function createTexturedDiceFaces(diceIndex: number): [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture] {
  const faces: DiceFaceTexture[] = [];
  
  for (let i = 1; i <= 6; i++) {
    faces.push({
      color: new THREE.Color(0xffffff),
      map: createDotTexture(i),
      normalMap: createNormalMap(i),
    });
  }
  
  return faces as [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture];
}

/**
 * 检查WebGL支持
 */
export function checkWebGLSupport(): boolean {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (e) {
    return false;
  }
}

/**
 * 获取设备性能等级
 */
export function getDevicePerformanceLevel(): 'low' | 'medium' | 'high' {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl');
  
  if (!gl) return 'low';
  
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  if (debugInfo) {
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
    
    // 简单的GPU性能检测
    if (renderer.includes('Intel') || renderer.includes('Mali')) {
      return 'low';
    } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
      return 'high';
    }
  }
  
  // 基于内存和CPU核心数的简单检测
  const memory = (navigator as any).deviceMemory || 4;
  const cores = navigator.hardwareConcurrency || 4;
  
  if (memory >= 8 && cores >= 8) {
    return 'high';
  } else if (memory >= 4 && cores >= 4) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * 根据设备性能调整配置
 */
export function adjustConfigForPerformance(
  config: DiceSimulationConfig, 
  performanceLevel: 'low' | 'medium' | 'high'
): DiceSimulationConfig {
  const adjustedConfig = { ...config };
  
  switch (performanceLevel) {
    case 'low':
      adjustedConfig.renderer.antialias = false;
      adjustedConfig.renderer.pixelRatio = 1;
      adjustedConfig.lighting.shadows = false;
      adjustedConfig.performance.targetFPS = 30;
      break;
    case 'medium':
      adjustedConfig.renderer.antialias = true;
      adjustedConfig.renderer.pixelRatio = Math.min((typeof window !== 'undefined' ? window.devicePixelRatio : 1) || 1, 1.5);
      adjustedConfig.lighting.shadows = true;
      adjustedConfig.performance.targetFPS = 45;
      break;
    case 'high':
      adjustedConfig.renderer.antialias = true;
      adjustedConfig.renderer.pixelRatio = Math.min((typeof window !== 'undefined' ? window.devicePixelRatio : 1) || 1, 2);
      adjustedConfig.lighting.shadows = true;
      adjustedConfig.performance.targetFPS = 60;
      break;
  }
  
  return adjustedConfig;
}

/**
 * 创建示例自定义纹理配置
 * 展示如何使用PNG图片作为骰子纹理
 */
export function createExampleTextureConfigs(): DiceTextureConfig[] {
  return [
    // 骰子1 - 尝试加载自定义图片，失败时使用彩色方案
    {
      diceId: 'dice-1',
      faces: [
        { imageUrl: '/textures/dice1/face1.png', color: 0xffffff}, // 温暖红色作为降级
        { imageUrl: '/textures/dice1/face2.png', color: 0xffffff }, // 青绿色作为降级
        { imageUrl: '/textures/dice1/face3.png', color: 0xffffff }, // 天蓝色作为降级
        { imageUrl: '/textures/dice1/face2.png', color: 0xef476f }, // 金黄色作为降级
        { imageUrl: '/textures/dice1/face2.png', color: 0xef476f }, // 橙色作为降级
        { imageUrl: '/textures/dice1/face2.png', color: 0xef476f }, // 深红色作为降级
      ]
    },
    // 骰子2 - 尝试加载自定义图片，失败时使用冷色调
    {
      diceId: 'dice-2',
      faces: [
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
      ]
    },
    // 骰子3 - 混合纯色和纹理
    {
      diceId: 'dice-3',
      faces: [
        { imageUrl: '/textures/dice1/face1.png', color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
        { color: 0xffffff },
      ]
    }
  ];
}
