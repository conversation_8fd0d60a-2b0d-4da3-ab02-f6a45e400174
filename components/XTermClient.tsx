'use client';

import { useEffect, useRef } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebglAddon } from '@xterm/addon-webgl';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { monoFont } from '@/app/fonts';
import { playAsciiAnimation } from './XT_AsciiAnimationPlugin';
import { typeText } from './XT_TypewriterPlugin';
import { useFloatingWindows } from '@/lib/floating-windows-store';
import { TerminalCommandManager } from '@/lib/terminal';
import {
    TerminalAuthPromptPlugin,
    TerminalSixelPlugin,
    globalOSC8LinkManager,
    createCommandLink,
    createUrlLink,
    createButtonCommandLink,
    createSuccessCommandLink,
    createWarningCommandLink,
    createErrorCommandLink,
    createImportantCommandLink,
    createCustomColorCommandLink,
    createRainbowCommandLink,
    createCleanCommandLink,
    createTextCommandLink,
    createLinkStyleCommandLink,
    LinkStyles
} from '@/lib/terminal/plugins';
import { globalThemeManager } from '@/lib/terminal/ThemeManager';
import { DefaultTheme } from '@/lib/terminal/themes';
import { useSupabase } from '@/components/SupabaseProvider';
import { Readline } from 'xterm-readline';
import { Unicode11Addon } from '@xterm/addon-unicode11';
import { globalMessageManager } from '@/lib/floating-window-messages';

// 全局状态：防止React Strict Mode导致的重复初始化
let globalTerminalInitialized = false;
let globalTerminalInstance: Terminal | null = null;

interface XTermClientProps {
    onTerminalReady?: (terminalInstance: Terminal, executeCommand: (command: string, source?: 'user' | 'window' | 'menu') => Promise<any>) => void;
}

export default function XTermClient({ onTerminalReady }: XTermClientProps = {}) {
    const divRef = useRef<HTMLDivElement>(null);
    const { addWindow, removeWindow, startClosing } = useFloatingWindows();
    const { supabase, user, session, isLoading } = useSupabase();
    const commandManagerRef = useRef<TerminalCommandManager | null>(null);
    const authPromptPluginRef = useRef<TerminalAuthPromptPlugin | null>(null);
    const supabaseRef = useRef(supabase);
    const terminalRef = useRef<Terminal | null>(null);
    const readlineRef = useRef<Readline | null>(null);
    const executeCommandRef = useRef<((command: string, source?: 'user' | 'window') => Promise<any>) | null>(null);
    const isInitializedRef = useRef<boolean>(false);
    const cleanupFunctionRef = useRef<(() => void) | null>(null);

    // 更新 supabase ref
    supabaseRef.current = supabase;

    // 创建终端命令处理器
    const handleTerminalCommand = async (command: string, params?: Record<string, any>): Promise<{ success: boolean; result?: any; error?: string }> => {
        const terminal = terminalRef.current;
        const readline = readlineRef.current;
        const executeCommand = executeCommandRef.current;

        if (!terminal || !readline || !executeCommand) {
            return {
                success: false,
                error: 'Terminal not ready'
            };
        }

        try {
            const result = await executeCommand(command, 'window');

            return {
                success: result.success,
                result: result.data,
                error: result.message
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    };

    // 组件挂载时的初始化设置
    useEffect(() => {
        // 页面卸载时的全局清理
        const handleBeforeUnload = () => {
            if (globalTerminalInstance) {
                globalTerminalInstance.dispose();
                globalTerminalInstance = null;
            }
            globalTerminalInitialized = false;
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        // 注册全局消息处理器
        const unsubscribers: Array<() => void> = [];
        const timer = setTimeout(() => {
            // Terminal命令处理器
            const unsubscribeCommand = globalMessageManager.on('TERMINAL_COMMAND', async (message) => {
                if (message.type !== 'TERMINAL_COMMAND') return;

                try {
                    const result = await handleTerminalCommand(message.payload.command, message.payload.params);

                    await globalMessageManager.sendTerminalResponse(
                        message.id,
                        result.success,
                        message.payload.command,
                        result.result,
                        result.error
                    );
                } catch (error) {
                    await globalMessageManager.sendTerminalResponse(
                        message.id,
                        false,
                        message.payload.command,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            });
            unsubscribers.push(unsubscribeCommand);

            // 浮窗操作响应处理器
            const unsubscribeResponse = globalMessageManager.on('WINDOW_OPERATION_RESPONSE', async (message) => {
                if (message.type !== 'WINDOW_OPERATION_RESPONSE') return;

                // 解析响应，用于Promise模式的命令
                globalMessageManager.resolveResponse(
                    message.payload.originalMessageId,
                    message.payload.result,
                    message.payload.error
                );
            });
            unsubscribers.push(unsubscribeResponse);

            // 🆕 窗口列表查询处理器
            const unsubscribeWindowList = globalMessageManager.on('WINDOW_LIST_QUERY', async (message) => {
                if (message.type !== 'WINDOW_LIST_QUERY') return;

                try {
                    // 获取当前所有窗口状态
                    const { windows } = useFloatingWindows.getState();
                    const { includeHidden } = message.payload;

                    // 过滤窗口（如果不包含隐藏窗口）
                    const filteredWindows = includeHidden
                        ? windows
                        : windows.filter(window => !window.isClosing && window.isVisible !== false);

                    // 发送响应
                    await globalMessageManager.sendWindowListResponse(message.id, filteredWindows);
                } catch (error) {
                    // 发送空列表作为错误处理
                    await globalMessageManager.sendWindowListResponse(message.id, []);
                }
            });
            unsubscribers.push(unsubscribeWindowList);

            // 窗口列表响应处理器（解决Promise）
            const unsubscribeWindowListResponse = globalMessageManager.on('WINDOW_LIST_RESPONSE', async (message) => {
                if (message.type !== 'WINDOW_LIST_RESPONSE') return;

                // 解析响应，用于Promise模式的命令
                globalMessageManager.resolveResponse(
                    message.payload.originalMessageId,
                    message.payload,
                    undefined
                );
            });
            unsubscribers.push(unsubscribeWindowListResponse);
        }, 1000);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
            clearTimeout(timer);
            unsubscribers.forEach(unsubscribe => unsubscribe());
        };
    }, []);

    // 监听认证状态变化，更新插件状态
    useEffect(() => {
        if (authPromptPluginRef.current) {
            const currentState = authPromptPluginRef.current.getAuthState();
            const hasUserChanged = currentState.user?.id !== user?.id;
            const hasSessionChanged = currentState.session?.access_token !== session?.access_token;

            if (hasUserChanged || hasSessionChanged) {
                authPromptPluginRef.current.setAuthState(user, session, false);
            }
        }
    }, [user, session]);

    useEffect(() => {
        if (!divRef.current) {
            return;
        }

        if (globalTerminalInitialized || globalTerminalInstance) {
            // 复用现有的terminal实例
            if (globalTerminalInstance) {
                terminalRef.current = globalTerminalInstance;

                // 重新绑定到当前容器
                if (divRef.current && !divRef.current.querySelector('.xterm')) {
                    globalTerminalInstance.open(divRef.current);
                }

                // 重新设置必要的引用
                const existingReadline = (globalTerminalInstance as any)._readline;
                if (existingReadline) {
                    readlineRef.current = existingReadline;
                }

                const existingExecuteCommand = (globalTerminalInstance as any)._executeCommand;
                if (existingExecuteCommand) {
                    executeCommandRef.current = existingExecuteCommand;
                }

                const existingCommandManager = (globalTerminalInstance as any)._commandManager;
                if (existingCommandManager) {
                    commandManagerRef.current = existingCommandManager;
                }
            }
            return;
        }

        // 标记为正在初始化
        globalTerminalInitialized = true;
        isInitializedRef.current = true;

        // 根据容器宽度和目标字符数计算合适的字体大小
        const calculateFontSize = (terminalInstance?: Terminal, fitAddonInstance?: FitAddon) => {
            if (!divRef.current) return 16; // 默认字体大小

            const containerWidth = divRef.current.offsetWidth;
            const containerHeight = divRef.current.offsetHeight;
            if (containerWidth <= 0 || containerHeight <= 0) return 16;

            // 根据屏幕宽度确定目标字符数
            const screenWidth = window.innerWidth;
            let targetCols: number;

            if (screenWidth < 720) {           // 竖屏手机
                targetCols = 50;
            } else if (screenWidth < 1280) {   // 平板/横屏手机
                targetCols = 80;
            } else {                           // 电脑屏幕
                targetCols = 120;
            }

            // 如果有 FitAddon 实例，使用 proposeDimensions 获取精确的字符尺寸
            if (fitAddonInstance && terminalInstance) {
                try {
                    // 使用 FitAddon 的 proposeDimensions 方法获取当前字体下的字符尺寸
                    const dimensions = (fitAddonInstance as any).proposeDimensions();
                    if (dimensions && dimensions.cols && dimensions.rows) {
                        // 计算当前字符的实际宽度
                        const currentCharWidth = containerWidth / dimensions.cols;
                        const currentCharHeight = containerHeight / dimensions.rows;

                        // 计算达到目标列数所需的字符宽度
                        const targetCharWidth = containerWidth / targetCols;

                        // 根据字符宽度比例调整字体大小
                        const currentFontSize = terminalInstance.options.fontSize || 16;
                        const scaleFactor = targetCharWidth / currentCharWidth;
                        const calculatedFontSize = currentFontSize * scaleFactor;

                        // 确保字体大小在合理范围内
                        const fontSize = Math.max(8, Math.min(32, Math.round(calculatedFontSize)));



                        return fontSize;
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }

            // 如果没有终端实例或 FitAddon，返回默认字体大小
            return 16;
        };

        // OSC 8 超链接管理器已在全局配置中处理

        // 主初始化函数
        const initTerminal = async () => {
            // 先创建一个临时终端来获取基础字体大小
            const tempTerm = new Terminal({
                fontFamily: `${monoFont.style.fontFamily}, monospace`,
                fontSize: 16, // 临时使用默认字体大小
                fontWeight: '600',
                lineHeight: 1.5,
                letterSpacing: 2,
            });

            // 创建临时 FitAddon 来计算字体大小
            const tempFitAddon = new FitAddon();
            tempTerm.loadAddon(tempFitAddon);
            tempTerm.open(divRef.current!);

            // 计算合适的字体大小
            const calculatedFontSize = calculateFontSize(tempTerm, tempFitAddon);

            // 清理临时终端
            tempTerm.dispose();

            // 使用计算出的字体大小创建正式终端
            const term = new Terminal({
                /** 光标闪烁 */
                cursorBlink: true,

                /* 1. 字体相关 --------------------------------------- */
                fontFamily: `${monoFont.style.fontFamily}, monospace`, // 直接使用字体名称
                fontSize: calculatedFontSize, // 使用计算出的字体大小
                fontWeight: '600',      // 字体粗细: 'normal' | 'bold' | '100'-'900'
                lineHeight: 1.5,        // 行高倍数，默认 1.0
                letterSpacing: 2,       // 额外字间距，单位 px
                minimumContrastRatio: 0,  // 关闭自动对比度调整，允许自定义前景色

                /* 2. 宽字符 & Emoji 支持 */
                allowProposedApi: true,   // 启用实验性API支持

                /* 3. 透明背景支持 --------------------------------------- */
                allowTransparency: true,  // 启用透明背景支持

                /* 4. OSC 8 超链接支持 --------------------------------------- */
                linkHandler: globalOSC8LinkManager.getTerminalLinkHandlerConfig(),

                /* 5. 窗口操作支持 --------------------------------------- */
                windowOptions: {
                    getCellSizePixels: true,  // 启用字符单元格尺寸获取功能
                },

                /* 6. 主题配色 --------------------------------------- */
                theme: (() => {
                    // 使用主题管理器的当前主题，去掉扩展属性
                    const currentTheme = globalThemeManager.getCurrentTheme();
                    const { name, description, ...themeConfig } = currentTheme;
                    // 设置透明背景
                    return {
                        ...themeConfig,
                        background: '#00000000' // 全透明背景
                    };
                })()
            });

            // 渲染器选择 (WebGL → Canvas fallback)
            let rendererType = 'Unknown';
            try {
                const webglAddon = new WebglAddon();
                term.loadAddon(webglAddon);
                rendererType = 'WebGL';
            } catch (e) {
                const { CanvasAddon } = await import('@xterm/addon-canvas');
                term.loadAddon(new CanvasAddon());
                rendererType = 'Canvas';
            }

            // 4. 适配插件配置
            const fitAddon = new FitAddon();
            term.loadAddon(fitAddon);
            term.open(divRef.current!);

            // 5. 初始化Unicode支持
            const unicodeAddon = new Unicode11Addon();
            term.loadAddon(unicodeAddon);
            term.unicode.activeVersion = '11';

            // 6. 加载 WebLinksAddon 支持自动识别 URL
            const webLinksAddon = new WebLinksAddon(
                (event: MouseEvent, uri: string) => globalOSC8LinkManager.handleLinkActivation(event, uri)
            );
            term.loadAddon(webLinksAddon);

            // 7. 初始化Sixel插件
            const sixelPlugin = new TerminalSixelPlugin(term, {
                enableSixel: true,
                maxImageWidth: 800,
                maxImageHeight: 600
            });
            sixelPlugin.initialize();

            // 将插件实例附加到终端对象，供命令使用
            (term as any).sixelPlugin = sixelPlugin;
            (term as any)._supabaseClient = supabase;
            (term as any).osc8LinkManager = globalOSC8LinkManager;

            // 8. 设置主题管理器
            globalThemeManager.setTerminal(term);

            // 延迟初始化认证提示符插件
            const initAuthPlugin = async () => {
                if (authPromptPluginRef.current) {
                    (term as any).authPromptPlugin = authPromptPluginRef.current;
                    return;
                }

                try {
                    const authPlugin = new TerminalAuthPromptPlugin();

                    if (user || session) {
                        await authPlugin.initialize(supabaseRef.current, user, session, false);
                    } else {
                        await authPlugin.initialize(supabaseRef.current);
                    }

                    authPromptPluginRef.current = authPlugin;
                    (term as any).authPromptPlugin = authPlugin;
                } catch (error) {
                    // 失败时设置默认的提示符
                    const fallbackPlugin = {
                        getCurrentPrompt: () => 'Guest => ',
                        getAuthState: () => ({ user: null, session: null, isLoading: false })
                    };
                    (term as any).authPromptPlugin = fallbackPlugin;
                }
            };

            // 延迟初始化认证插件
            setTimeout(() => {
                initAuthPlugin().catch(() => {
                    // 静默处理错误
                });
            }, 500);


            // 阻止 xterm 容器的过度滚动事件传播
            const terminalElement = divRef.current!;
            const preventOverscroll = (e: WheelEvent) => {
                const terminal = terminalElement.querySelector('.xterm-viewport') as HTMLElement;
                if (terminal) {
                    const { scrollTop, scrollHeight, clientHeight } = terminal;
                    const isAtTop = scrollTop === 0;
                    const isAtBottom = scrollTop + clientHeight >= scrollHeight;

                    if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }
            };

            terminalElement.addEventListener('wheel', preventOverscroll, { passive: false });

            // 8. 延迟调用fit以确保容器已经渲染完成
            setTimeout(async () => {
                try {
                    if (divRef.current && divRef.current.offsetWidth > 0) {
                        fitAddon.fit();

                        // await typeText(term, '┌──────┐\n', 8);
                        // await typeText(term, '│      │\n', 8);
                        // await typeText(term, '└──────┘\n', 8);

                        // term.writeln(`┌──────────────────────────────────────────────────────────────────────────┐`);
                        // term.writeln(`│ \x1b[1minteractives\x1b[0m is a research journal focused on HCI, GAMES, and NEW MEDIA. │`);
                        // term.writeln(`└──────────────────────────────────────────────────────────────────────────┘`);
                        //


                        // await typeText(term, '\n', 30);
                        // await typeText(term, 'Here, you can edit and publish:\n', 30);
                        // await typeText(term, '\n', 3);
                        // term.write(`* ${createCommandLink('Preprints', 'open', { type: 'test' })}`);
                        // await typeText(term, '\n', 3);
                        // term.write(`* ${createCommandLink('Gallery shorts', 'open', { type: 'test' })}`);
                        // await typeText(term, '\n', 3);
                        // term.write(`* ${createCommandLink('Full-length papers', 'open', { type: 'test' })}`);
                        // await typeText(term, '\n', 3);
                        // await typeText(term, '\n', 30);

                        // await typeText(term, 'We believe a journal should be a living lab.\n', 30);
                        // await typeText(term, 'That’s why this terminal-style site is both your reading portal and part of the experiment itself.\n', 30);
                        // await typeText(term, 'Curious? Start with our introductory paper.\n', 30);
                        // await typeText(term, 'Ready to play? Type \'', 30);
                        // term.write(`${createCommandLink('\x1b[1;31mai\x1b[0m', 'ai',)}`);
                        // await typeText(term, '\' below and hit Enter to begin your journey!\n', 30);
                        // await typeText(term, '\n', 3);

                        // === 显示Sixel演示图像 ===
                        // if (sixelPlugin.getSixelSupport()) {
                        //     const demoSixelData = "\"1;1;627;108#0;2;0;6;91#1;2;6;12;91#2;2;12;18;92#3;2;18;24;92#4;2;25;29;93#5;2;31;35;93#6;2;37;41;94#7;2;44;47;95#8;2;50;53;95#9;2;56;59;96#10;2;62;65;96#11;2;69;71;97#12;2;75;76;98#13;2;81;82;98#14;2;87;88;99#15;2;94;94;99#16;2;100;100;100#16!627~-!21N!606~$#12!20O_$#0!20_#15O-#0!20~#12~#16!400~#4}!19@}#16!185~$#7!421?@#0!19}#7@-#0!20~#12~#16!84~^!14@!265~N!14@!21~#4~#0!19~#4~#16!185~$#11!105?_#0o!12{#8{#14!265?OA#12!12A{$#4!106?G#12!12A#14A#10!265?_#0o!12{#15A$#8!106?C#2!279?G$#15!106?A#6!279?C-#0!20~#12~#16!83~B#0{!13~#8~#16!263~^B#1A#12!13?~#16!21~#4~#0!19~#4~#16!185~$#1!104?_#3A#14!277?_#0_{!13~$#6!104?O#7@#4!278?O#5@$#11!104?G#9!279?G$#15!104?C#13!279?C-#0F!18~N#16w!9~!21^!6~^^NNN!11F!4N^^!16~!4^NNF@#2A#8!15?^#16!14^!22~^^!4N!13F!4N^^^!22~!21^!6~^NNN!9FNNN^!16~^^^!4N!14F!4N^^^!38~^^!5N!12F!4N^^^!19~!4^NNF@#1A#12!15?^#16!14^!7~!21^!4~!22^!23~!20^!18~^^^!4N!12F!4N^^^!31~^^^!4N!14F!5N^^!13~$#3G!18?O#12B#0!9?!20_!9?___!12o___#6_#14!17?_???O#4O#9G#7C#10@#4!29?_#10!22?_??O#6O#2O#12?G!9?G#14G#1O#5O#9O??_#15_#8!22?_#0!19_!9?__!9o__#2_#12!17?_??O!4?G#10G!8?G#12G!4?O#1_#6_#13_#10!38?_??O#6O#3O#12?GG!8?G!4?O#2_#14?_#13!19?_#11_#8_!5?@!29?_!7?_!19?_#9!4?_#4!20?_#6!23?_#0!18_#1_#12!18?_!7?G!8?G!4?O#1_#14?_#13!31?_#6_#1_#0___!16o!4_#5_$#4O!18?_#15C#12!29?_#10!6?_!6?G#8!6G#11G#4??O#9O#14O?_#12!17?_#8_#3_#0__ow{!15~!14_#5!24?_#0!4_!13o!4_#2_#8!44?_#9!6?_#1_#10O#3O!9?O#9O#15O#6!18?_#1_#0___!16o___#5!42?_#0!4_!14o___#3!25?_?O#6G#5C#6!147?_#1_#0___!14o___#12!37?O!4?G#10G!8?G!8?_$#7_#3!57?_??O#15G#12G!8?G#15!29?A!55?O???G#11?G#8!7G#10G#13!5?O#15!53?O#14??G!7?G???_#8!20?O!5?!8G!5?O#15!44?O#8!6?!8G#13?G#4?O#7O??_#0!24?__ow{!15~!14_!9?!19_!6?!20_#11!65?O#7O#3O#13?G#8?!7G#9G#13?G#4?O#7O??_#8!36?O!5?!8G#12?G#14G#4?O#7O#11O#15O$#13!59?O#7O#11!175?G!5?G#4!26?O#14?G!12?G#4?O#12!92?O#15??A#4!208?O#14?G$#8!237?!5G-@#0^!17~#16_!10~#0!20~??ow{}}!22~}{wo_!9?}!39~#4~#16!13~^NFBB@#15@#6!28?@#1A??G#10GO_#8!14?~#0!19~??_ow{}!15~N#16}!8~NFFB@@#12@#4!29?@#12@#16@@BFN^!21~^NFBB@#1A?@#6!26?@#14@#16@BBFN^!8~#4@#3@#8!39?~!7?~!19?~#16!4~}w#1G#14!19?@G#5!19?_C#1@#5!16?_#16_{!9~^NFBB@@#2?@#4!26?@#12@#16@@BFN^!15~^NFB@@#12@#4@#1!28?@?A???O_$#12E#4_#7!17?@#10!30?_O#6GC#9A#13@#4@#7!22?@#15@??G#10_#4!7?}#3@#6!53?_#0_oow{{}}!26~}{{woo_#8!35?~#16^FB@#4?@#3!15?O!9?_O#6G#9C#13A#4A?@#5!30?A#1C??_#6!22?_O#9G??A#15@#8@!29?A#3C#1G#11G#10O_#16!50?!7~#15!25?@#5A#7O#3!19?C#16F!19~F@#2!18?C#6@#10!9?_!6?@#6!29?A???O_#10!15?_#0_oww{}}!28~}}{{wo_$#16w#8!18?A#12!30?^#16NFB@#3!25?A?G#5O#7!8?@#8!55?O!5?@#14!28?@#16@BBFN^!14~#12!21?_#15G??@#4!16?_#14@#15!8?OG!37?A#13C??_#0!22?_oww{{}}!26~}}{woo_!9?}}!39~!9?!19~!6?@F!19~w#2_#10!19?O#0w}!16~^B#13A#0!10?__ow{{}}!26~}}{wwo_#6!17?O#8G#11C#1C#5A#8!31?@#15@??C??_$#11!19?C#1!31?_#16!29?@BF^!7~#11!57?G#1G??A?@#8!28?A#3C#13C#5!39?O#13CA#0!28?_ow{{}}!28~}}{wwo#13!26?C#3C#13!34?C#11!86?C#13_#9!19?A#8O#15!19?G#11A#9!18?G#11!11?O#1O#3G??A#1!31?C#11C#7G#14!21?A#16!34?@BBFN^!4~$#12!19?G#1!61?C#13C#14!67?C#4C#10A#2!74?G#1CA#10!67?GO#14!210?O#13!12?G#15C#7C#12A#15!32?A#10!58?A#5C#2G#13GO$#15!19?O#14!61?A-#16~_#0!16~#3@#16!11~#0!20~}!33~}w!7?!40~#4~!9?O#1G#8A#6@#1!43?A#16@FN!10~#8~#0!19~{}!20~!7?ow}!43~}w_#13_#10!13?_?C#6A@#1!43?A#16@F^!4~#8!41?~!7?~!19?~#16!7~w_#1_#2!18?C#16F!15~F#0w!17~N@!9?ow}!43~}wo#2_!9?G#3A!16?_#1O!8?O#2?_#3!15?@#16@B^~$#4?@!16?A#2!31?@!33?@C#16F!5~#4~#16!41?!8~^FB@#10!44?@#3C#8G#6_#4!30?A#2@#4!20?B#16!5~NB@#2!44?@?O#0!15?_o{}!43~{w_!5?!41~!9?!19~!9?F^!18~w#1_#3!15?_C#8!17?_!9?O#3G#12A#10@#6!43?@#10A?O!9?C#15!18?_#16!9_#1!18?CO#11_$#8?E#7!16?C#10!66?A#3_#10!55?_#0_o{}!43~{wo#14O#8!30?@!21?{#7!5?_#6G?@#8!44?AG#4!15?O#1G#13!45?@#5C#2O#10_#16!46?!7~#3!28?@#5G#7!19?A#6O#9!15?OA#2!17?O#1A!8?_?C#16!45?@BN!8~NB#6!19?O#10O!7?_#8!17?AG$#12?G#8!16?G#15!66?@#7O#14!56?G#11C#13!46?A#14!60?O#15C#1C#15!45?@C#13!15?G#15!47?A#11G#9!82?A#10O#13!19?@#12G#14!15?G#15@#16!18?o{!6~^NB@#1!45?C#13C!9?O?@#12!20?!4O#8O#5O#15!20?C$#13?O#11!16?O#13!67?G#10!167?A#1!45?C#16B^!13~^FB@#14!129?C#6!57?C#10@#14!6?_?C#4!47?G#8!9?_#0o{!16~^!10N^^!15~}w_$#12!18?_!488?G#15A-#16~~#1@#0!14~B#16}!11~#0!22~NFBB!7@BBF^!20~!6?!7@!19~!14@!8?w}!16~NFB@#15A#7@#12@#13!4?@???C#10G#4O#7!17?AO#8!8?~#0!24~^^!11N^^^~!5?o}!16~^FB@@!6?@@BN!18~w#16N!10~F@#1!18?_#7O#16ow{{!6}{{wwo#3O#2!15?@#16@N~~!7}#12!20?}#16!14}!7~#8!20?~#16!9~{_#5_#1!18?C#16B^!10~F#0w!17~F@!8?w}!16~^FB@@!9?@@BF^!16~{_!6?}!17~o!4_#4_!9?A#11!7?O#13!7?O?G$#4??A#3!14?C#15@#3!33?O!9?A?CG#6_#16!20?@!4~!7}#8}!19?}#16!14}!6~F@#2!17?O???@!8?@#0@@BN!17~w#16F!8~#4!25?_#12_???!5O!7?F???_#6C#1@#2!16?_#16_w{{!6}{wo#4_#1!18?C#12_#2!10?_C@#15!18?_#10G#9C!9?A#1A#6CG#13_#1!16?G#8_!9?}!33?@!7?~#6!29?@#0B^!18~w_#7!11?OA#6!17?O!9?C@!16?_?C??@#11@#15@???@???C?_#3!17?AO#15_#4!5?@#6!17?E!5?_!7?@?G#3!15?A$#5??C#4!14?G#13!34?_#16_ow!7{wo_#7!21?_#4!4?@!40?@#1!6?_#2C@#10!16?_?C#9!9?@?A#1AC#12_#2!17?C#3_#16!35?!11_!4?o~~~N@#12!18?O!9?A#2A#1CO#4!18?A#13O#7!10?O#0w}!17~^NFB@@@!5?@@@BFN!15~}o???!8@!19~!14@!9?!19~#13!10?A#2C#6!19?A#15C_#13!10?G@#1!17?G#16o}!6~F@!18?_w{{}}}~~~}}}{wo#1G_#10!16?@#16B^!5~#1!18?HO#10O#14O#8???_!9?!6O#6!9?@$#8??G#7!14?O#15!35?OG!9?G#14O#8!21?W#6!52?O#10A#16!18?ow{{}}!4~}}}{wo#14!18?@#13G#2!35?O#6O#9O!5?O???_#2_!6?G#3!18?G!8?@#15?C#10G#8!19?@#13!11?G#10A#4!22?A??!4@#2@#14A#15?C#6!19?C#12O#9!82?G#11!19?@#5O#1!11?_#2C#11!17?_#10C!7?O#14!19?O??A#10!7?@#4@#12A#2A#3C#8O#9!18?G#16!25?ENN!6^~~~}}w!16_ov$#9??O#8!14?_#4!35?G#2C?A#7A???A#11!25?C!52?G#8!19?G?A#7!77?O#3O#14_#13???G#15???O#11A#6!19?C#5A?@#8@@@#7@#11!60?A!28?A#14!83?O#10!20?G#15!31?G!7?G#4!19?G#5?A#14!12?G!19?C#15!25?@!7?_???@#0?@F!15NK$#12??_!52?C!7?C!23?A#14!185?A#4!231?A#14@!7?A#13!79?G#12!6?_!7?O!7?!7O$#8!58?AAA#5!452?_#14!94?C-#16~~}#0B!12~N#16w!12~#0!20~F#16{!14~}#0B!19~#4~#16!11~#8~#0!19~#8~#16!18~N#0w!18~x!19w!18~!9?!20~FB@#9@#10!16?@#12!6?@@@#11@#8@@@#6@#4@@@!8?O!5?G#2!5?C#12!19?~#16!9~#0_!19~B#16{!17~{!8_!9o!9~#0?!19~#12~#16!21~#8~#0!19~#8~#16!11~{o#2O#4!18?A#16B^!5~^F#1C#3!17?G!9?O!18?C#12!18C#5@#4!18?w!6?_#1!26?@#4@#7@!8?C#4?G?O#13O#8_$#13??@#2C#3!12?O#11@#4!32?G#5@#13!14?@#4C#12!70?_#4C!18?A#12!18C#1@#8!18?w!7?~#1!20?G#16o{}!16~}!6~!11}!4]KKKNNN!6F!4B#8B#3!29?O#8!19?O@#5!17?@G#8OO#9O#14???O#3???G#4GGG?@#10!83?@#0@N!18~{_!7?_w!17~F!9?_!18~z!19w!18~!6?@^!26~!6}!4{wwwoo__$#4???G!12?_#12A#8!32?o#11A#6!15?G#14!70?O#7A#6!18?C#16A!17B#4A#16!18?@!7~#6!21?O#7C#10A#1!35?@!8?O!5?G#4!34?G#2!19?C#14A#11!17?A#0B!10N!5FC#15!83?A#1A#8_#10!18?@#14C_#15!5?_#11G@#15!17?_!9?@#16!19?A!17B#8A#7!18?C#16!5~_#9!30?@!4?A#14A??C$#6???O#15!13?C#8!49?o#11!71?@#15!19?@#6!17?C#11!18?C#12!28?_#15G#13!36?_#0@@@``!4_!6o!5w!19~#7!10?C#6!19?G#12!19?O???OOO#8!8?GG!9?~#6!74?C#3!20?O#5!7?O#6A#10!17?O#16w!8~#4G#11!19?@#9!17?C#8!18?A#2!5?A#12!31?@!9?G$#8???_#12!192?A#9!67?_!4?O!5?G!5?C#10!31?A#12!19?_#1!19?C#2!15?A#12!84?G#9!20?G#2!27?@#8!8?C#12!56?@#5!5?C#15!32?@#16!4@BBBFFNN^!6~$#5!265?_#2_#12AQ!5?G!5?C#14!32?@#7!189?A#11!8?A#9!62?G#2!33?A???C$#15!267?O!5?G!5?C#14!223?C!71?O#5!34?A$#7!269?@O!5?G!5?C$#14!269?A-#16~~~w#0N!11^#16_!13~#0!20~#12~#16!16~#8~#0!19~#4~#16!11~#8~#0!19~#8~#16!18~#8[#0!57~!9?!19~@#16!27~^NFFB@@@#12@#6@#12!41?~#16!9~#0!20~#11_#16!45~#8~#0!19~#12~#16!21~#8~#0!19~#8~#16!13~}o#1G#2!18?AO#11_#14?_C#5A#3!16?_#16_{!9~`#0!57~!8?@BFN^^!38~}{w_#8_$#10???@#2O#4!11_#7@#9!121?A#8!57?~!7?~#3!19?A#10!27?_O#13G#2G???A??@#12!70?^#14!121?@#0@F!18~{_???_{!16~^B#6@#12!9?[#4!57?~#16!6~}wo__#9_#1_#5!38?@#15@??O$#12???A#7_#8!11?A#11!121?_#16!58?!7~#4!20?C#0!28?_ooww{{}}}!40~#4!153?A#6O#7!18?@G#3???O#10@#2!17?C#11A#14!9?A#12!64?@#15CGO#4O#3!41?A#16BFN~$#14???C#11!12?C#12!121?@#6!85?G#8!31?C#1C#9A#10!197?C#11_#16!19?B^~^B#7!18?G!76?A#3CG#6!43?C#1O$#12!16?G#8!207?o#15!32?A!198?G#13!20?C#9???G#14!18?O#11!123?G$#15!16?O-#16!21^!9~#0!20~#12~#16!16~#8~#0!19~#4~#16!11~#8~#0!19~#3_#16!18~{#0F!18~o#8!25@PP!8@``!8?~#0!19~#8~#16!24~F@#1A#8!16?_???C???A!6?A#12!19?~#16!9~_#0^!18~w_#14_#10!15?_#0_{!14}{C#8!9?~#0!19~#5_#16!21~#8~#0!19~#8~#16!16~w#0F!18~{w{!16~NB#9@#0!12?@!18~w#16E]!18}]!9M!8]~~~!9^]]][[!4WO!5o!7_#7?_#1_#2!18?@_$#4!20_#13_#7!98?O#12!18?B#2G!18?G#16M!19}!10M!8]!7~#14!45?GA#10@#1!16?OG!4?A#5A#11?A!4?_#6@#3!29?@_#1!18?C#16F^!15~^B#3A#1@#4@@@#8@@@#11@#12@@#14@#16!5@!9~#8!20?O#2!58?@#3G#1!18?A#8C#2A#1!16?O#16o{!12~#1A#3!18?C#8P!24@PP!9@`#10@#15???_!8?@!4?C???G!5?O#9?O#2??O#4!23?O$#8!119?N#5!19?O#6!18?@#3_#14!19?O!8?O!7?_#3!52?_#0w{!16~NFFBBB!6@???o!19~#4!10?A#5!19?A#6O#2!17?O#7@#4!14?AA#11!29?G#7!58?A#10O#6!18?@#16B#9@#7!16?_#5C#15A#4!12?C!18?@#14G_#9!18?_#0!11_#2_#7??_#8!15?@!4?___??G!6?O#5!25?G$#9!139?_#7!18?C#12O#5!19?_#0!11_#3_#7!57?O#6C#16!18?_owww!5{}}}]#8!30?C!19?@#13G#9!17?G#3!16?G#12!29?F#14!58?C#15_#11!38?G#6!13?G!18?A#1_#3!20?O#4OO!9?__#1!17?@#0@@!4B!5F!7N!4^!18~}#9C$#11!158?A#2!21?O#4OO!9?__#13!75?O???C#14!4?A#4?@??G#11!29?G#15!38?C#5!16?O#10!142?O#6!43?O#11??O#12O!8?_!4?!14_!4?G!5?O!5?_#13!21?A$#7!183?O#12??OO!8?@#9!73?G#2C#7!9?@?C#14!29?O#9!55?_#14!142?_#13!48?O#10!23?A??C#6C!4?G#4G!5?O#16!24?@$#9!282?@#5!303?A#2???C#13_-#0!20~#12~#16!9~#0!20~#12~#16!16~#8~#0!19~#4~#16!11~#8N#0!20~{!4woo!5w{{!6?@F^!17~}{wwooo!5_!4ow{{!15~N@!9?!19~#8~#16!23~@#0!19~o_!8?__ow}!20~_!6?_!4?BN!18~}{w!10oww{}!15~N@#8!10?^#0!20~{!4wo!6w{{!8?!19~#8~#16!18~{_#3_#5!32?O#16o}!15~w_#3!18?@#16@@BFFF!6NFFFB@#13@#1!15?O#10G!5?O#2!16?A#16BFN^^!9~]K#0r!17~B$#10!99?O#3!20?A#16@BB!4F!4B@@@!4~}o#1G_#7!17?@#16@BBFFF!6NFFFB@#1A#4!15?O#16o}!7~#8~#12!43?}#16!19?AN^^~~~^^^NFB@#7!21?G?_!8?@#4C#12_#4!18?@#16@BBFF!4NFFFB@#3A#1@#3!15?O#16o}!9~#9_#14!20?@!7?C#11C#7C#13?A#6A#8!7?~#6!38?@#0B^!32~N@#13@#2!15?@#0F^!17~}{ww!4o!4_!4oww{}!14~NB!5?B!16~{o__#6_#14_#10!9?_#3@G#2!17?C$#12!99?_!20?@#1C!4?G#14?C???A!6?@#15G_#8!19?A#4C!5?OOO#2O???G#8C#10?@#12!15?_#3A#15@#14!71?D!6?_#10_#4_!4?@#1!20?O_#8?__#6_#2_!6?O#6!19?A#5C#15C!6?G???A#14@#10!16?_#13G@#3!30?A#16@BB!4F!4B@@!7~#11!39?A#2C#10!33?_#3A#7!16?A#4G_#5!18?A#2C#12C#4G???OOO#3O???G??A#7!15?_#15OA!4?_#7!16?@#1GO#12O#15!11?@#1_#4C!17?G$#15!121?A?C#4?G#11???C#7C??A#12{#9!5?C#8O#14!21?C#6G#11G#15G#1O#12!5?G???A#7!17?C#8!72?G#12O!8?O??A!21?F#16N!5^N~~~{o#1!22?G#7G#12G!7?C#2C#1!18?A#2!32?C!9?C#7!49?G#9!34?C#13!16?C#12O#15!20?A#10??G#14G!6?G???A#16!17?_{~~~}#2C#10!17?C#13G#12!13?O#6!18?O$#10!122?C#3!8?C!8?A#7!33?G#14!20?G#6!74?_#13_#15!7?GC#2C#15!22?O#13!5?O#14???A#10G!30?G#4G#6!20?C#15!32?A?C#4?G#13!55?O#15!34?G#9!50?G#11?C#1C?@#3!15?C#11@#13???@#5G#14!32?A#9!18?_$#13!134?A#1!144?O#3G#10!121?C-#0!20~#12~#16!9~#0!20~#12~#16!16~#8~#0!19~#4~#16!11~}_#1O#12!32?~#16!7~}wo_#1!43?_O#16_o{!10~#8~#0!19~#8~#16!23~}_#6_#8!32?_#12O#4_#16!25?!6~{wo_#1!41?_O#16_o{!12~{_#6_#16!32?!7~#8~!19?~#16!20~{o#1O#2!28?G#16w!20~}{o_#10!44?_O#3C#8A#15@#6!7?@#0BF^!18~!9}!17~^F@#14@$#13!99?@#0BN!32~!9?@BN^!42~^NFB#4@#12!54?@#0B^!32~^N^!25~!7?@FN^!40~^NFB#12A!12?@#0B^!32~!8?!19~#9!21?@#0BN!28~F#1@#13!20?@??O_#16!44?_o{}!7~{o_#10_#3!18?@#7@#8@???@#6@#2@#10!17?_?C$#5!100?C#8_#9!40?@#3A#1C#10O_#13!43?_O#7C#12A#3!55?C#16!34?_#5!32?@#1A#10G#6O_#13!41?_O#7C#15!13?A#3C#15!81?A#5C#7_#9!28?O#6A#0!21?@BFN^!42~^FB#12!9?A#5C#1G#12!22?@@@#16!21?_w}$#10!100?G#14!42?C#11G#3!46?G#15G#8!56?G#15!67?A#12C#3!45?G?@#7!13?G#11!82?G#14!29?_#13C#7!21?A#3C#1GO_!43?G?@#14!9?G#11O#3!46?G#4A$#15!100?O#14!148?O#15!115?G#14!14?O!136?G#13!47?G!58?O-#0!20~#12~#16!9~#0!20^#12^#16!16~#8^#4!19_^#16!13~}{wo__#1!20?_#7_???G#2C#12B#11!12?@#1@#0@BFFNN^^^!21~^^^NNFFB@#1@#8!15?^#0!19^#8^#16!25~}{wo__#14_#1!17?_#6_#12_!8?@#7A??O#13_#5_#3!14?_???G#10!11?@#0@@BFFNN^^^!20~^^NNFFB@#13A@#14!18?@#10A!4?_#2!19?_#7_#14_?O#8G!8?^#4!19_#8^#16!22~}o#5O#4!23_#5_?@#13!27?@#15A??G?O#6???_#1!21?_!7?A#10!18?@A??G?O#2O??_#5!23?_#10_#1O???C#12C#9A$#4!30?!20_#13_#10!16?_#0!19^#7_#15!13?@#1@??G#4O?_#14!20?_?O??C#13!13?A#3A#8C#14G?O#15?_#10_#5_#2!21?_#7_!5?C#3A#10!16?_#4!19_#10_#15!25?@???O#7O?_#16!19?__oww{~~}{oo_#9!17?_#1O#16_o!11~}{wwoo__#14_#8_#3_#5!20?_!5?C#15C#1@!20?@#0BFNN^^!18~^^^NFFB!8?!19^#10_#13!22?@#0@N!24^B#10A#3!28?@#0@BBFFNN^^^!20~^^^NNFFB@@!18?@@BFFNN^^^!23~^^NNNFBB@$#12!102?A#0BFFN^^!18~^^^NNFB#6!18?G?O#13!26?_!6?A@#1!61?@?C#2G???_#4!18?O??C?@#15!25?_#8O#12!13?A??G?O#11!25?_???G#6!24?C#9G#12O#2O?_#16!21?__ow!7~#10_#3!43?A#11_#16!24?_{!27~}{{woo___#6!24?_#11_!6?A#13!19?C!5?_#7_#15!26?_???G$#10!103?CG??_#16!22?__oow!12~}{{woo__!27?__ooww{}!14~#13!47?A?G!24?O#8G#3?A!5?CG#2!35?A#6C#4?G?O#16!25?__ooww{}!18~}{wo__#6!23?O#1G#3?C#9!51?C#1!25?C#5!30?A#11C!5?_#16!25?__ooww{}!16~}{wwo___!29?__oow{}!4~$#13!105?O#5!24?O#15??G#4!48?O?G#15??C#0!64?BBFN^^^!15~^^^NNFB@!4?@BFN^^!14~^^NNF#15!13?C#2!30?O?G??A#14!104?G#6!25?G#1!32?C?G?O??_#2!23?O?G#4?C#14C#1!20?A??G#6!31?O!5?@$#12!183?O?G#10!66?C#15!28?CA!4?G#10!68?O#12!134?O#8!33?G?O#10!28?O?G???@#3!19?C#15?O#14!32?O#7G-#16!21{!88~!4}!9{!5}!31~!6}!10{!5}!80~!15}!18~!14}!27~!5}!9{!6}!36~!4}!9{!5}!125~!5}!10{!5}!38~!6}!11{!5}!15~$#12!20A@#14!88?@#9@#4@#1@#0!10@#1@#4@#8@#15!32?@#11@#8@#4@#2@#0!12@#4@#6@#10@#14@#13!80?@#8@#5@#3@!7?@#4@#8@#12@#14!18?@#9@#5@#2@#0!6@#4@#6@#9@#14@!27?@#10@#6@#4@#0!11@#1@#4@#7@#11@#15@#13!36?@#8@#4@#1@#0!10@#2@#5@#8@#12!126?@!5?!8A#14A#3?@#5@#8@#13@#14!38?@!5?A#12!9A!5?@$#0!20@#15A!92?A#12!8A!4?@#14!37?A#12!8A#15A#0!89?!7@#15!68?A#12!8A#15!46?A#12!7A#13A!4?@#8!126?@#5@#3@#0!12@#10!43?@#7@#4@#1@#0!13@#3@#5@#8@$#15!537?A!68?A-#16!627~-!627~-";
                        //     sixelPlugin.displaySixelImage(demoSixelData);
                        // }

                        // 显示 INTERACTIVES ASCII Banner
                        // term.writeln('');
                        // term.writeln('██╗███╗   ██╗████████╗███████╗██████╗  █████╗  ██████╗████████╗██╗██╗   ██╗███████╗███████╗');
                        // term.writeln('██║████╗  ██║╚══██╔══╝██╔════╝██╔══██╗██╔══██╗██╔════╝╚══██╔══╝██║██║   ██║██╔════╝██╔════╝');
                        // term.writeln('██║██╔██╗ ██║   ██║   █████╗  ██████╔╝███████║██║        ██║   ██║██║   ██║█████╗  ███████╗');
                        // term.writeln('╚═╝██║╚██╗██║   ██║   ██╔══╝  ██╔══██╗██╔══██║██║        ██║   ██║╚██╗ ██╔╝██╔══╝  ╚════██║');
                        // term.writeln('██╗██║ ╚████║   ██║   ███████╗██║  ██║██║  ██║╚██████╗   ██║   ██║ ╚████╔╝ ███████╗███████║');
                        // term.writeln('╚═╝╚═╝  ╚═══╝   ╚═╝   ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝   ╚═╝   ╚═╝  ╚═══╝  ╚══════╝╚══════╝');
                        // term.writeln('');

                        // term.writeln('');
                        // term.writeln('░▒▓█▓▒░▒▓███████▓▒░▒▓████████▓▒░▒▓███████▓▒░ ░▒▓██████▓▒░ ░▒▓██████▓▒░▒▓████████▓▒░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓████████▓▒░░▒▓███████▓▒░');
                        // term.writeln('░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░      ░▒▓█▓▒░       ');
                        // term.writeln('░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒▒▓█▓▒░░▒▓█▓▒░      ░▒▓█▓▒░       ');
                        // term.writeln('░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓███████▓▒░░▒▓████████▓▒░▒▓█▓▒░        ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒▒▓█▓▒░░▒▓██████▓▒░  ░▒▓██████▓▒░ ');
                        // term.writeln('░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░        ░▒▓█▓▒░   ░▒▓█▓▒░ ░▒▓█▓▓█▓▒░ ░▒▓█▓▒░             ░▒▓█▓▒░');
                        // term.writeln('      ░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░ ░▒▓█▓▓█▓▒░ ░▒▓█▓▒░             ░▒▓█▓▒░');
                        // term.writeln('░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░░▒▓██████▓▒░  ░▒▓█▓▒░   ░▒▓█▓▒░  ░▒▓██▓▒░  ░▒▓████████▓▒░▒▓███████▓▒░ ');
                        // term.writeln('');

                        await executeCommand('ai', 'user');

                        // // 显示基本信息
                        // await typeText(term, '🖥️  XTerm.js Terminal Initialized\n', 30);
                        // await typeText(term, `📊 Renderer: ${rendererType} Mode\n`);
                        await typeText(term, `📱 Screen: ${window.innerWidth}x${window.innerHeight}px\n`);

                        // 显示字体大小和字符数信息
                        const currentFontSize = (term.options as any).fontSize;
                        const screenWidth = window.innerWidth;
                        let targetCols: number;
                        if (screenWidth < 640) {
                            targetCols = 50;
                        } else if (screenWidth < 1280) {
                            targetCols = 100;
                        } else {
                            targetCols = 150;
                        }
                        await typeText(term, `🔤 Font: ${currentFontSize}px (target: ${targetCols} cols)\n`);
                        await typeText(term, `📐 Terminal Size: ${term.cols} cols × ${term.rows} rows\n`);
                        // await typeText(term, `\n💡 输入 'help' 查看可用命令\n`);
                        // await typeText(term, `🎨 主题管理: 输入 'theme' 查看和切换主题\n`);

                        // await typeText(term, `🎨 主题颜色演示:\n• \x1b[31m红色警告\x1b[0m\n• \x1b[32m绿色成功\x1b[0m  \n• \x1b[33m黄色提示\x1b[0m\n• \x1b[94m明亮蓝色信息\x1b[0m\n• \x1b[95m明亮品红特殊\x1b[0m\n`);

                        // // === OSC 8 超链接测试 ===
                        // await typeText(term, `\n🔗 OSC 8 超链接测试 (支持丰富样式):\n`);

                        // // 基础命令链接
                        // term.writeln(`基础样式: ${createCommandLink('【help 命令】', 'help')}`);
                        // term.writeln(`打开浮窗: ${createCommandLink('【open 命令】', 'open', { type: 'test' })}`);
                        // term.writeln(`AI模式: ${createCommandLink('【ai 命令】', 'ai')}`);
                        // term.writeln(`主题管理: ${createCommandLink('【theme 命令】', 'theme')}`);
                        // term.writeln(`切换深色: ${createCommandLink('【深色主题】', 'theme', { action: 'set', name: 'dark' })}`);
                        // term.writeln(`矩阵主题: ${createCommandLink('【矩阵主题】', 'theme', { action: 'set', name: 'matrix' })}`);

                        // // 样式化命令链接
                        // await typeText(term, `\n🎨 样式化链接演示:\n`);
                        // term.writeln(`按钮样式: ${createButtonCommandLink(' 帮助 ', 'help')}`);
                        // term.writeln(`成功样式: ${createSuccessCommandLink('✅ 系统状态', 'status')}`);
                        // term.writeln(`警告样式: ${createWarningCommandLink('⚠️ 清理缓存', 'clean')}`);
                        // term.writeln(`错误样式: ${createErrorCommandLink('❌ 测试错误', 'error')}`);
                        // term.writeln(`重要样式: ${createImportantCommandLink('🔥 重要操作', 'restart')}`);

                        // // 自定义颜色链接
                        // term.writeln(`自定义橙色: ${createCustomColorCommandLink('🧡 橙色链接', 'help', '255,165,0')}`);
                        // term.writeln(`自定义紫色: ${createCustomColorCommandLink('💜 紫色链接', 'help', '128,0,128')}`);

                        // // 彩虹样式（每次颜色随机）
                        // term.writeln(`彩虹样式: ${createRainbowCommandLink('🌈 彩虹链接', 'help')}`);
                        // term.writeln(`另一个彩虹: ${createRainbowCommandLink('✨ 随机颜色', 'help')}`);

                        // // 使用预设样式对象
                        // term.writeln(`信息样式: ${createCommandLink('ℹ️ 信息链接', 'help', {}, { style: LinkStyles.info })}`);
                        // term.writeln(`突出样式: ${createCommandLink('⭐ 突出链接', 'help', {}, { style: LinkStyles.highlight })}`);

                        // // 文本样式测试
                        // await typeText(term, `\n🎨 文本样式:\n`);
                        // term.writeln(`简洁样式: ${createCleanCommandLink('🔵 简洁链接', 'help')}`);
                        // term.writeln(`文本样式: ${createTextCommandLink('📝 文本链接', 'help')}`);
                        // term.writeln(`链接样式: ${createLinkStyleCommandLink('🔗 链接样式', 'help')}`);

                        // // 外部链接样式
                        // await typeText(term, `\n🌐 外部链接样式:\n`);
                        // term.writeln(`默认GitHub: ${createUrlLink('【GitHub】', 'https://github.com')}`);
                        // term.writeln(`按钮GitHub: ${globalOSC8LinkManager.createUrlLink('【GitHub按钮】', 'https://github.com', { style: LinkStyles.button })}`);
                        // term.writeln(`重要文档: ${globalOSC8LinkManager.createUrlLink('【重要文档】', 'https://docs.example.com', { style: LinkStyles.important })}`);

                        // // 邮件链接
                        // term.writeln(`邮件链接: ${globalOSC8LinkManager.createEmailLink('【发送邮件】', '<EMAIL>', { style: LinkStyles.info })}`);

                        // // 提示用户如何使用
                        // await typeText(term, `\n💡 提示: 按住 Ctrl 键并点击上面的任意样式链接，体验丰富的视觉效果！\n`);

                        // // === 播放 ASCII 动画（使用插件） ===
                        // // await playAsciiAnimation(term);

                        // 启动命令处理循环
                        setTimeout(() => {
                            showPromptAndWaitForInput().catch(() => {
                                // 静默处理错误
                            });
                        }, 200);
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }, 100);

            // 9. 初始化命令管理器和readline
            if (!commandManagerRef.current) {
                commandManagerRef.current = new TerminalCommandManager();
                commandManagerRef.current.setAddWindowFunction(addWindow);
                commandManagerRef.current.setRemoveWindowFunction(removeWindow);
                commandManagerRef.current.setStartClosingFunction(startClosing);
            }

            const commandManager = commandManagerRef.current;

            // 创建并配置 readline
            const rl = new Readline();
            term.loadAddon(rl);

            // 🎲 设置键盘输入回调用于骰子动画
            const keyboardCallbacks: Array<(key: string) => void> = [];

            // 添加键盘输入监听器
            term.onData((data: string) => {
                // 触发所有注册的键盘回调
                if (keyboardCallbacks.length > 0) {
                    // console.log('[XTermClient] 键盘输入:', data, '回调数量:', keyboardCallbacks.length);
                }
                keyboardCallbacks.forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        // console.error('[XTermClient] 键盘回调错误:', error);
                    }
                });
            });

            // 将键盘回调注册函数添加到terminal实例
            (term as any).addKeyboardCallback = (callback: (key: string) => void) => {
                // console.log('[XTermClient] 注册键盘回调，当前回调数量:', keyboardCallbacks.length);
                keyboardCallbacks.push(callback);
                // console.log('[XTermClient] 注册完成，新的回调数量:', keyboardCallbacks.length);
                return () => {
                    const index = keyboardCallbacks.indexOf(callback);
                    if (index > -1) {
                        keyboardCallbacks.splice(index, 1);
                        // console.log('[XTermClient] 取消注册键盘回调，剩余回调数量:', keyboardCallbacks.length);
                    }
                };
            };

            // 保存引用供消息处理使用
            terminalRef.current = term;
            readlineRef.current = rl;
            globalTerminalInstance = term;

            // 获取当前提示符的辅助函数
            const getCurrentPrompt = () => {
                // 获取用户名
                const authPlugin = (term as any).authPromptPlugin;
                let userName = 'Guest';

                if (authPlugin && typeof authPlugin.getAuthState === 'function') {
                    try {
                        const authState = authPlugin.getAuthState();
                        if (authState.user) {
                            // 获取用户显示名称
                            userName = authState.user.user_metadata?.name ||
                                authState.user.email?.split('@')[0] ||
                                'User';
                        }
                    } catch (error) {
                        // 静默处理错误，使用默认用户名
                    }
                }

                // 检查是否在AI模式
                if (commandManager.isInAIMode()) {
                    return `${userName} (AI) => `;
                }

                // 普通模式下的提示符
                return `${userName} => `;
            };

            // 创建扩展terminal对象的辅助函数
            const createExtendedTerminal = () => ({
                ...term,
                commandManager,
                // 确保原始终端属性可访问
                buffer: term.buffer,
                rows: term.rows,
                cols: term.cols,
                options: term.options,
                element: term.element,
                // 添加readline实例供LoginCommand使用
                readline: rl,
                // 扩展方法
                writeln: (data: string) => {
                    if (data.startsWith('\r\n')) {
                        rl.println(data.substring(2));
                    } else {
                        rl.println(data);
                    }
                },
                // 添加write方法支持流式输出
                write: (data: string) => {
                    rl.write(data);
                },
                clear: () => {
                    // 使用ANSI转义序列清屏，与readline兼容
                    rl.write('\x1b[2J\x1b[H');
                },
                // 重启prompt循环的方法
                restartPromptLoop: () => {
                    setTimeout(() => {
                        showPromptAndWaitForInput().catch(() => {
                            // 静默处理错误
                        });
                    }, 10);
                },
                // 原始终端引用
                _originalTerminal: term
            });

            // 将terminal实例设置到OSC 8管理器中，让链接能够访问终端
            globalOSC8LinkManager.setTerminalInstance(createExtendedTerminal());

            // 命令执行的核心函数
            const executeCommand = async (command: string, source: 'user' | 'window' | 'menu' = 'user') => {
                if (!command.trim()) {
                    return { success: true };
                }

                const actualCommand = command.trim();

                try {
                    const extendedTerm = createExtendedTerminal();
                    // 🚀 传递source参数给命令管理器，支持智能路由
                    const result = await commandManager.executeCommand(actualCommand, extendedTerm, source);

                    if (!result.success && result.message) {
                        rl.println(result.message);
                    }

                    return result;
                } catch (error) {
                    const errorMsg = source === 'window'
                        ? '❌ 来自浮窗的命令执行出错: ' + (error instanceof Error ? error.message : '未知错误')
                        : source === 'menu'
                            ? '❌ 来自菜单的命令执行出错: ' + (error instanceof Error ? error.message : '未知错误')
                            : '❌ 命令执行出错: ' + (error instanceof Error ? error.message : '未知错误');
                    rl.println(errorMsg);

                    return { success: false, message: error instanceof Error ? error.message : '未知错误' };
                }
            };

            // 显示提示符并等待输入的函数
            const showPromptAndWaitForInput = async () => {
                try {
                    const currentPrompt = getCurrentPrompt();
                    const input = await rl.read(currentPrompt);

                    if (input.trim()) {
                        await executeCommand(input.trim(), 'user');
                    }

                    setTimeout(showPromptAndWaitForInput, 10);
                } catch (error) {
                    // 用户按 Ctrl+C 或其他中断操作
                    if (error instanceof Error && error.message.includes('canceled')) {
                        rl.println('');
                    }

                    setTimeout(showPromptAndWaitForInput, 100);
                }
            };

            // 保存引用供外部使用和复用
            executeCommandRef.current = executeCommand;
            (term as any)._readline = rl;
            (term as any)._executeCommand = executeCommand;
            (term as any)._commandManager = commandManager;

            // 🆕 通知父组件terminal已准备就绪
            if (onTerminalReady) {
                onTerminalReady(term, executeCommand);
            }

            // 监听窗口变化并自动调整字体大小以保持目标字符数
            const handleResize = () => {
                try {
                    if (divRef.current && divRef.current.offsetWidth > 0) {
                        const newFontSize = calculateFontSize(term, fitAddon);
                        const currentFontSize = (term.options as any).fontSize;

                        if (Math.abs(newFontSize - currentFontSize) > 0.5) { // 避免频繁的微小调整
                            term.options.fontSize = newFontSize;

                            // 先调用fit来重新计算终端尺寸
                            fitAddon.fit();

                            // 然后刷新显示
                            term.refresh(0, term.rows - 1);


                        } else {
                            // 即使字体大小没变，也要调用fit以适应容器大小变化
                            fitAddon.fit();
                        }
                    }
                } catch (error) {
                    // 静默处理错误
                }
            };

            window.addEventListener('resize', handleResize);

            return () => {
                window.removeEventListener('resize', handleResize);
                terminalElement.removeEventListener('wheel', preventOverscroll);

                // 清理Sixel插件
                if (sixelPlugin) {
                    sixelPlugin.dispose();
                }

                // 清理认证提示符插件
                const authPlugin = (term as any).authPromptPlugin;
                if (authPlugin && typeof authPlugin.dispose === 'function') {
                    authPlugin.dispose();
                }

                // 清理主题管理器 (移除终端引用，但不销毁全局实例)
                globalThemeManager.setTerminal(null as any);

                term.dispose();
            };
        };

        // 启动初始化
        initTerminal().then(cleanupFn => {
            cleanupFunctionRef.current = cleanupFn;
        }).catch(error => {
            isInitializedRef.current = false;
        });

        return () => {
            // 清理本地引用，保留全局状态供复用
            terminalRef.current = null;
            readlineRef.current = null;
            executeCommandRef.current = null;

            if (authPromptPluginRef.current) {
                authPromptPluginRef.current.dispose();
                authPromptPluginRef.current = null;
            }
        };
    }, []);

    return (
        <div
            ref={divRef}
            className="h-full w-full overscroll-none"
            style={{
                background: 'transparent'
            }}
        />
    );
}